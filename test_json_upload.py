#!/usr/bin/env python3
"""
Test script for the new JSON upload functionality.
This script tests that data can be uploaded as JSON and properly loaded as DataFrame variables 
in the sandbox without local file creation.
"""

import asyncio
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sandbox_client import SandboxClient


async def test_json_upload():
    """Test the new upload_json_data functionality."""
    print("Starting JSON upload test...")
    
    # Create test data
    headers = ["name", "age", "city", "score"]
    rows = [
        {"name": "Alice", "age": 25, "city": "New York", "score": 85.5},
        {"name": "<PERSON>", "age": 30, "city": "San Francisco", "score": 92.0},
        {"name": "<PERSON>", "age": 35, "city": "Chicago", "score": 78.3},
        {"name": "<PERSON>", "age": 28, "city": "Boston", "score": 88.7}
    ]
    
    session_id = "test_json_upload_session"
    variable_name = "test_data"
    file_name = "test_data"
    
    try:
        # Initialize client
        client = SandboxClient()
        
        # Create session
        print(f"Creating session: {session_id}")
        create_result = await client.create_session(session_id)
        if not create_result.get("success", True):
            print(f"Failed to create session: {create_result}")
            return False
        
        # Upload JSON data
        print(f"Uploading JSON data with {len(rows)} rows and {len(headers)} columns...")
        upload_result = await client.upload_json_data(
            session_id=session_id,
            headers=headers,
            rows=rows,
            variable_name=variable_name,
            file_name=file_name
        )
        
        if not upload_result.get("success"):
            print(f"Upload failed: {upload_result}")
            return False
        
        print(f"Upload successful! Result: {upload_result}")
        
        # Verify the data was loaded correctly
        verification_code = f"""
import pandas as pd

# Check if variable exists
if '{variable_name}' in globals():
    df = globals()['{variable_name}']
    print(f"SUCCESS: DataFrame '{variable_name}' found with shape {{df.shape}}")
    print(f"Columns: {{list(df.columns)}}")
    print(f"Data types: {{df.dtypes.to_dict()}}")
    print("First few rows:")
    print(df.head())
    
    # Verify data integrity
    expected_rows = {len(rows)}
    expected_cols = {len(headers)}
    if df.shape == (expected_rows, expected_cols):
        print("✓ Shape matches expected dimensions")
    else:
        print(f"✗ Shape mismatch: expected ({expected_rows}, {expected_cols}), got {{df.shape}}")
    
    # Check if all expected columns are present
    expected_columns = {set(headers)}
    actual_columns = set(df.columns)
    if actual_columns == expected_columns:
        print("✓ All expected columns present")
    else:
        print(f"✗ Column mismatch: expected {{expected_columns}}, got {{actual_columns}}")
        
else:
    print(f"ERROR: Variable '{variable_name}' not found in globals")
    print(f"Available variables: {{list(globals().keys())}}")
"""
        
        print("Executing verification code...")
        verify_result = await client.execute_code(session_id, verification_code, timeout_seconds=15)
        
        if verify_result.get("success"):
            print("Verification completed successfully!")
            print("Output:")
            print(verify_result.get("output", "No output"))
            return True
        else:
            print(f"Verification failed: {verify_result}")
            return False
            
    except Exception as e:
        print(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_generate_details_csv():
    """Test the refactored generate_details_csv function."""
    print("\nTesting generate_details_csv function...")
    
    # Import the function
    from parser_graph import generate_details_csv
    
    # Create test data in the format expected by generate_details_csv
    test_data = {
        "details": [
            {
                "company": "Company A",
                "sector": "Technology",
                "indicators": [
                    {"key": "revenue", "value": "1000000"},
                    {"key": "employees", "value": "500"}
                ]
            },
            {
                "company": "Company B", 
                "sector": "Finance",
                "indicators": [
                    {"key": "revenue", "value": "2000000"},
                    {"key": "employees", "value": "800"}
                ]
            }
        ]
    }
    
    session_id = "test_generate_details_session"
    file_name = "test_companies"
    
    try:
        # Call the function (file_path is not used anymore but kept for compatibility)
        result = generate_details_csv(test_data, "/tmp", file_name, session_id)
        
        if result:
            local_path, sandbox_path = result
            print(f"generate_details_csv completed successfully!")
            print(f"Local path: {local_path} (should be None)")
            print(f"Sandbox path: {sandbox_path}")
            return True
        else:
            print("generate_details_csv returned None")
            return False
            
    except Exception as e:
        print(f"generate_details_csv test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing JSON Upload Functionality")
    print("=" * 60)
    
    # Test 1: Direct JSON upload
    test1_success = await test_json_upload()
    
    # Test 2: generate_details_csv function
    test2_success = await test_generate_details_csv()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"Direct JSON upload test: {'PASSED' if test1_success else 'FAILED'}")
    print(f"generate_details_csv test: {'PASSED' if test2_success else 'FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests PASSED! JSON upload functionality is working correctly.")
        return True
    else:
        print("\n❌ Some tests FAILED. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
