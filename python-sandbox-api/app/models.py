"""
Data models for the Python Sandbox API.
"""
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field


class CodeExecutionRequest(BaseModel):
    """Model for code execution requests."""
    code: str = Field(..., description="Python code to execute")
    timeout_seconds: Optional[int] = Field(30, description="Maximum execution time in seconds")


class CodeExecutionResponse(BaseModel):
    """Model for code execution responses."""
    success: bool = Field(..., description="Whether the execution was successful")
    output: str = Field(..., description="Output from code execution (stdout, stderr)")
    variables: Optional[Dict[str, Any]] = Field(None, description="Variables defined in the session. For DataFrames, includes detailed information like shape, columns and dtypes.")
    plots: Optional[List[str]] = Field(None, description="Paths to any generated plots")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    memory_usage: Optional[float] = Field(None, description="Memory usage in MB")


class SessionInfo(BaseModel):
    """Model for session information."""
    session_id: str = Field(..., description="Session identifier")
    created_at: datetime = Field(..., description="When the session was created")
    expires_at: datetime = Field(..., description="When the session will expire if inactive")
    variables: Optional[Dict[str, Any]] = Field(None, description="Variables defined in the session. For DataFrames, includes detailed information like shape, columns and dtypes.")


class LibraryInstallRequest(BaseModel):
    """Model for library installation requests."""
    libraries: List[str] = Field(..., description="List of libraries to install")


class LibraryInstallResponse(BaseModel):
    """Model for library installation responses."""
    success: bool = Field(..., description="Whether the installation was successful")
    output: str = Field(..., description="Output from the installation process")
    installed_libraries: List[str] = Field(..., description="Libraries that were successfully installed")


class JsonUploadRequest(BaseModel):
    """Model for JSON data upload requests."""
    headers: List[str] = Field(..., description="Column headers for the DataFrame")
    rows: List[Dict[str, Any]] = Field(..., description="Data rows as list of dictionaries")
    variable_name: str = Field(..., description="Variable name to bind the DataFrame to")
    file_name: Optional[str] = Field(None, description="Optional filename for the CSV file (without extension)")