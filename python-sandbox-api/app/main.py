import os
import traceback
from typing import Any, Optional, List
from contextlib import asynccontextmanager

import uvicorn
from fastapi import <PERSON>AP<PERSON>, HTTPException, UploadFile, File, Form, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse

from app.session import Session<PERSON>anager, Session
from app.executor import execute_code_in_session
from app.models import (
    CodeExecutionRequest,
    CodeExecutionResponse,
    SessionInfo,
    LibraryInstallRequest,
    LibraryInstallResponse,
    JsonUploadRequest,
)

# Setup

session_manager = SessionManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    await session_manager.start_cleanup_task()
    yield
    await session_manager.shutdown()

# Initialize FastAPI app with lifespan

app = FastAPI(
    title="Python Sandbox API",
    description="API for executing Python code in isolated environments",
    version="1.0.0",
    lifespan=lifespan,
)

# Add CORS middleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency to get active session

async def get_active_session(session_id: str):
    session = await session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail=f"Session {session_id} not found or expired")
    return session

# Routes

@app.post("/sessions", response_model=SessionInfo, status_code=status.HTTP_201_CREATED)
async def create_session(session_id: Optional[str] = None):
    """Create a new Python execution session."""
    session = await session_manager.create_session(session_id)
    return {
        "session_id": session.id,
        "created_at": session.created_at,
        "expires_at": session.expires_at
    }

@app.get("/sessions/{session_id}", response_model=SessionInfo)
async def get_session(session: Any = Depends(get_active_session)):
    """Get information about an existing session."""
    return {
        "session_id": session.id,
        "created_at": session.created_at,
        "expires_at": session.expires_at,
        "variables": await session.get_variable_info(),
    }

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Explicitly delete a session."""
    success = await session_manager.close_session(session_id)
    if not success:
        raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
    return {"success": True, "message": f"Session {session_id} deleted"}

@app.post("/sessions/{session_id}/execute", response_model=CodeExecutionResponse)
async def execute_code(
    request: CodeExecutionRequest,
    session: Any = Depends(get_active_session),
):
    """Execute Python code in a session."""
    try:
        return await execute_code_in_session(session, request.code, request.timeout_seconds)
    except Exception as e:
        tb = traceback.format_exc()
        return {
            "success": False,
            "output": f"Error executing code: {e}\n\n{tb}",
            "variables": await session.get_variable_info(),
            "error": str(e)
        }

@app.post("/libraries/install", response_model=LibraryInstallResponse)
async def install_library_global(request: LibraryInstallRequest):
    """Install Python libraries globally for all sessions."""
    try:
        # Call the SessionManager's install_library method directly
        result = await session_manager.install_library(request.libraries)
        return {
            "success": result["success"],
            "output": result["output"],
            "installed_libraries": result["installed"] if result["success"] else []
        }
    except Exception as e:
        return {
            "success": False,
            "output": f"Error installing libraries: {e}",
            "installed_libraries": []
        }


@app.post("/sessions/{session_id}/upload")
async def upload_file(
    session_id: str,
    file: UploadFile = File(...),
    variable_name: Optional[str] = Form(None),
    session: Any = Depends(get_active_session),
):
    """Upload a file to a session."""
    try:
        result = await session.upload_file(file, variable_name)
        return {
            "success": True,
            "filename": result["filename"],
            "variable": result.get("variable")
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading file: {e}")


@app.post("/sessions/{session_id}/upload_json")
async def upload_json_data(
    session_id: str,
    request: JsonUploadRequest,
    session: Any = Depends(get_active_session),
):
    """Upload JSON data as DataFrame to a session."""
    try:
        import pandas as pd
        import os

        # Create DataFrame from headers and rows
        df = pd.DataFrame(request.rows, columns=request.headers)

        # Generate filename if not provided
        file_name = request.file_name or f"data_{session_id}"
        if not file_name.endswith('.csv'):
            file_name += '.csv'

        # Save as CSV in session files directory
        csv_path = os.path.join(session.files_dir, file_name)
        df.to_csv(csv_path, index=False)

        # Load DataFrame into session globals with variable name
        variable_name = request.variable_name
        if variable_name.endswith('.csv'):
            variable_name = variable_name[:-4]  # Remove .csv extension for variable name

        session.globals[variable_name] = df

        return {
            "success": True,
            "filename": file_name,
            "variable": f"{variable_name} (DataFrame)",
            "shape": df.shape,
            "columns": list(df.columns)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading JSON data: {e}")

@app.get("/sessions/{session_id}/files/{filename}")
async def download_file(
    filename: str,
    session: Any = Depends(get_active_session),
):
    """Download a file from a session."""
    path = await session.get_file_path(filename)
    if not path or not os.path.exists(path):
        raise HTTPException(status_code=404, detail=f"File {filename} not found")
    return FileResponse(path, filename=filename)

@app.get("/sessions/{session_id}/files")
async def list_files(session: Any = Depends(get_active_session)):
    """List all files in a session."""
    return {"files": await session.list_files()}

@app.get("/sessions")
async def list_sessions():
    """List all active sessions."""
    return {"sessions": await session_manager.list_sessions()}

@app.get("/sessions/{session_id}/plots/{filename}")
async def download_plot(
    session_id: str,
    filename: str,
    session: Session = Depends(get_active_session)
):
    """Download a plot (Pickle or image) from that session's plots directory."""
    path = await session.get_file_path(filename)
    if not path:
        raise HTTPException(status_code=404, detail=f"Plot {filename} not found in session {session_id}")
    return FileResponse(path, filename=filename)

@app.get("/health")
def health():
    """Basic health check endpoint."""
    return {"ok": True}

if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8004, reload=True)
