# Generic Parser Comparison Periods Documentation

## Overview

The Generic Parser (GP) system allows multiple comparison periods to be calculated for a single request. This document explains how comparison periods work and how their date ranges are determined.

## Key Concepts

### What are Comparison Periods?

Comparison periods allow you to analyze data across different time ranges relative to your main request period. For example, you might want to compare current week data with:
- Previous week
- Same week last year
- Multiple previous periods

### Why are Comparison Periods Important?

The number of comparison periods determines how many algorithm requests need to be sent. Each comparison period generates a separate request with its own start and end dates.

## Process Flow

```mermaid
flowchart TD
    A[Generic Parser Request] --> B{Contains comparison_periods?}
    B -->|Yes| C[Parse each comparison period]
    B -->|No| D[Use current period only]
    
    C --> E[Calculate date ranges for each period]
    E --> F[Generate algorithm requests]
    F --> G[Log requests with X-NRG-CONVID]
    
    D --> F
```

## Comparison Period Types

### 1. Current Period
The base period from your original request.

```mermaid
flowchart LR
    A[Original Request] --> B[Current Period]
    B --> C[Start: Request start_date<br/>End: Request end_date]
```

### 2. Previous Period Calculations

#### Previous Period 1 & 2
These periods shift backwards by the duration of the original request.

```mermaid
flowchart TD
    A[Original Period<br/>Nov 18-20, 2020] --> B[Duration: 3 days]
    B --> C[Previous Period 1<br/>Nov 15-17, 2020]
    B --> D[Previous Period 2<br/>Nov 12-14, 2020]
```

**Formula:**
- Duration = end_date - start_date
- Previous Period N:
  - Start: original_start - (N × duration)
  - End: original_end - (N × duration)

### 3. Previous Time Resolution Period

This period depends on the `time_resolution` parameter:

```mermaid
flowchart TD
    A[Time Resolution] --> B{Resolution Type}
    B -->|Yearly| C[Previous Year]
    B -->|Monthly| D[Previous Month]
    B -->|Weekly| E[Previous Week]
    B -->|Daily| F[Previous Day]
    B -->|1_hour| G[Previous Hour]
    B -->|Invalid/Missing| H[Error: PREVIOUS_TIME_RESOLUTION_PERIOD_IMPOSSIBLE]
```

### 4. Year-Based Comparisons

#### Previous Year
Simply shifts the dates to the previous year:

```mermaid
flowchart LR
    A[2020-11-18 to 2020-11-20] --> B[2019-11-18 to 2019-11-20]
```

**Special Case:** February 29th → February 28th in non-leap years

#### Specific Year

Two scenarios based on whether the period spans multiple years:

```mermaid
flowchart TD
    A[Specific Year Request] --> B{Same Year?}
    B -->|Yes| C[Single Request<br/>Replace year values]
    B -->|No| D[Multiple Requests<br/>One per year]
    
    D --> E[Year 1: Start date to Dec 31]
    D --> F[Year 2+: Jan 1 to Dec 31]
    D --> G[Last Year: Jan 1 to End date]
```

## Date Calculation Examples

### Example Request
```json
{
  "start_date": "2020-11-18T00:00",
  "end_date": "2020-11-20T23:59",
  "comparison_periods": ["current", "previous_period_1", "previous_year"],
  "time_resolution": "monthly"
}
```

### Calculated Periods

| Comparison Period | Start Date | End Date |
|------------------|------------|----------|
| current | 2020-11-18T00:00 | 2020-11-20T23:59 |
| previous_period_1 | 2020-11-15T00:00 | 2020-11-17T23:59 |
| previous_period_2 | 2020-11-12T00:00 | 2020-11-14T23:59 |
| previous_time_resolution_period | 2020-10-18T00:00 | 2020-10-20T23:59 |
| previous_year | 2019-11-18T00:00 | 2019-11-20T23:59 |

## Time Resolution Reference

| Resolution | Calculation Method |
|------------|-------------------|
| yearly | Subtract 1 year from dates |
| monthly | Subtract 1 month from dates |
| weekly | Subtract 7 days from dates |
| daily | Subtract 1 day from dates |
| 1_hour* | Subtract 1 hour from dates |

*Note: 1_hour retrieves data for the last hour of the previous day

## Special Date Handling

### Leap Year Considerations

When dealing with February 29th:
- If the target year is not a leap year, use February 28th
- This applies to all year-based calculations

```mermaid
flowchart LR
    A[Feb 29, 2020] --> B{Target Year Leap?}
    B -->|Yes| C[Feb 29, Target Year]
    B -->|No| D[Feb 28, Target Year]
```

## Implementation Notes

1. **Logging**: All requests must be logged with the X-NRG-CONVID identifier
2. **Error Handling**: Return `PREVIOUS_TIME_RESOLUTION_PERIOD_IMPOSSIBLE` when time_resolution is invalid or missing
3. **Multiple Requests**: For overlapping year periods, create separate requests for each year

## Summary

The Generic Parser comparison periods system provides flexible date range calculations for comparative analysis. By understanding these period types and their calculation methods, you can effectively configure requests to retrieve the exact data ranges needed for your analysis.