#!/usr/bin/env python3
"""
Debug script to inspect what's actually in a sandbox session.
This will help us understand why no plots are being found.

Usage:
    python debug_session.py <session_id>
"""

import sys
import os
import asyncio
import argparse
import json
from typing import Dict, Any

# Add the current directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from sandbox_client import SandboxClient
    import aiohttp
except ImportError as e:
    print(f"Error importing required modules: {e}")
    sys.exit(1)


class SessionDebugger:
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.sandbox_client = SandboxClient()
    
    async def debug_session(self):
        """Debug session information and contents."""
        try:
            print(f"🔍 Debugging session: {self.session_id}")
            print("=" * 60)
            
            # 1. Get session info
            print("\n1. 📋 Session Info:")
            try:
                session_info = await self.sandbox_client.get_session_info(self.session_id)
                print(f"   Raw response: {json.dumps(session_info, indent=2)}")
                
                if "error" in session_info:
                    print(f"   ❌ Session error: {session_info['error']}")
                    return
                
                # Extract key information
                plots = session_info.get("plots", [])
                variables = session_info.get("variables", {})
                files = session_info.get("files", [])
                
                print(f"   📊 Plots found: {len(plots)}")
                for i, plot in enumerate(plots):
                    print(f"      {i+1}. {plot}")
                
                print(f"   📁 Files found: {len(files)}")
                for i, file in enumerate(files):
                    print(f"      {i+1}. {file}")
                
                print(f"   🔢 Variables found: {len(variables)}")
                for var_name, var_info in variables.items():
                    var_type = var_info.get('type', 'unknown') if isinstance(var_info, dict) else type(var_info).__name__
                    print(f"      {var_name}: {var_type}")
                
            except Exception as e:
                print(f"   ❌ Error getting session info: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # 2. Try direct API calls to the sandbox
            print("\n2. 🌐 Direct API Calls:")
            try:
                base_url = self.sandbox_client.base_url
                
                # Try to list sessions
                async with aiohttp.ClientSession() as session:
                    # Get session details
                    url = f"{base_url}/sessions/{self.session_id}"
                    print(f"   Calling: {url}")
                    
                    async with session.get(url) as response:
                        if response.status == 200:
                            data = await response.json()
                            print(f"   ✅ Session exists: {json.dumps(data, indent=2)}")
                        else:
                            print(f"   ❌ Session API call failed: {response.status}")
                            text = await response.text()
                            print(f"   Response: {text}")
                    
                    # Try to list plots directory
                    plots_url = f"{base_url}/sessions/{self.session_id}/plots"
                    print(f"   Trying plots endpoint: {plots_url}")
                    
                    try:
                        async with session.get(plots_url) as response:
                            if response.status == 200:
                                data = await response.json()
                                print(f"   ✅ Plots endpoint response: {json.dumps(data, indent=2)}")
                            else:
                                print(f"   ❌ Plots endpoint failed: {response.status}")
                                text = await response.text()
                                print(f"   Response: {text}")
                    except Exception as e:
                        print(f"   ⚠️  Plots endpoint not available: {str(e)}")
                        
            except Exception as e:
                print(f"   ❌ Error with direct API calls: {str(e)}")
            
            # 3. Check if we can execute code to list files
            print("\n3. 🐍 Execute Code to List Files:")
            try:
                code = """
import os
import glob

# Check current directory
print("Current directory:", os.getcwd())

# List all files in current directory
print("\\nFiles in current directory:")
for item in os.listdir('.'):
    print(f"  {item}")

# Look for plots directory
plots_dir = os.path.expanduser('~/plots')
if os.path.exists(plots_dir):
    print(f"\\nFiles in {plots_dir}:")
    for item in os.listdir(plots_dir):
        print(f"  {item}")
else:
    print(f"\\nPlots directory {plots_dir} does not exist")

# Look for any pickle files
print("\\nSearching for .pickle files:")
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith('.pickle'):
            full_path = os.path.join(root, file)
            print(f"  Found: {full_path}")

# Check if there are any plotly figures in globals
print("\\nChecking for Plotly figures in globals:")
import sys
for name, obj in globals().items():
    if hasattr(obj, '__class__'):
        class_name = obj.__class__.__name__
        if 'plotly' in class_name.lower() or 'figure' in class_name.lower():
            print(f"  {name}: {class_name}")
"""
                
                result = await self.sandbox_client.execute_code(
                    session_id=self.session_id,
                    code=code,
                    timeout_seconds=10
                )
                
                print(f"   Execution result: {json.dumps(result, indent=2)}")
                
            except Exception as e:
                print(f"   ❌ Error executing code: {str(e)}")
            
            # 4. Summary
            print("\n4. 📝 Summary:")
            print(f"   Session ID: {self.session_id}")
            print(f"   Sandbox URL: {self.sandbox_client.base_url}")
            
            if plots:
                print(f"   ✅ Found {len(plots)} plots in session info")
                filtered = [p for p in plots if not p.startswith("plotly_plotly")]
                print(f"   📊 {len(filtered)} plots after filtering (excluding plotly_plotly*)")
            else:
                print("   ❌ No plots found in session info")
                print("   💡 Possible reasons:")
                print("      - Session hasn't generated any plots yet")
                print("      - Plots are saved but not tracked in session info")
                print("      - Session ID is incorrect")
                print("      - Sandbox service issue")
            
        except Exception as e:
            print(f"❌ Error debugging session: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # Clean up
            if hasattr(self.sandbox_client, '_session') and self.sandbox_client._session:
                await self.sandbox_client._session.close()


async def main():
    parser = argparse.ArgumentParser(
        description="Debug a sandbox session to see what's inside",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python debug_session.py c3373e37-404b-4f05-be65-bcec8322df5e
        """
    )
    
    parser.add_argument(
        "session_id",
        help="The session ID to debug"
    )
    
    args = parser.parse_args()
    
    if not args.session_id:
        print("Error: Session ID is required")
        parser.print_help()
        sys.exit(1)
    
    debugger = SessionDebugger(args.session_id)
    await debugger.debug_session()


if __name__ == "__main__":
    asyncio.run(main())
