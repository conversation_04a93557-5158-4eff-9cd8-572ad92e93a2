# Session Plot Viewer Scripts

Two scripts to view and analyze plots generated in sandbox sessions, excluding plots that start with "plotly_plotly".

## Scripts Overview

### 1. `view_session_plots.py` - Combined View
- Shows all plots in a single browser window
- Creates subplots for multiple plots
- Good for quick overview and comparison

### 2. `view_individual_plots.py` - Individual View  
- Creates separate HTML files for each plot
- Generates an index page with links to all plots
- Better for detailed analysis of individual plots
- Files can be shared or archived

## Requirements

Make sure you have the required dependencies:
```bash
pip install plotly aiohttp
```

## Usage

### Combined View (All plots in one page)

```bash
# View all plots for a session in combined view
python view_session_plots.py 07d7ea02-ca2b-4874-9760-ce881ba454ab

# Just list available plots without displaying
python view_session_plots.py 07d7ea02-ca2b-4874-9760-ce881ba454ab --list-only
```

### Individual View (Separate files for each plot)

```bash
# Create individual HTML files for each plot
python view_individual_plots.py 07d7ea02-ca2b-4874-9760-ce881ba454ab

# Just list available plots without downloading
python view_individual_plots.py 07d7ea02-ca2b-4874-9760-ce881ba454ab --list-only
```

## Features

### Filtering
- ✅ Includes plots ending with `.pickle`
- ✅ Includes plots containing "plotly" or "fig" in the name
- ❌ Excludes plots starting with "plotly_plotly"
- ❌ Excludes non-plot files

### Styling
- Applies company styling using `apply_company_style()`
- Responsive design for different screen sizes
- Clean, professional appearance

### Output

#### Combined View (`view_session_plots.py`)
- Opens a single HTML file in your browser
- Shows all plots in a grid layout
- Temporary file (can be deleted after viewing)

#### Individual View (`view_individual_plots.py`)
- Creates a folder: `session_plots_{session_id_prefix}/`
- Generates individual HTML files for each plot
- Creates an `index.html` with links to all plots
- Files are permanent and can be shared

## Examples

### Example 1: Quick Overview
```bash
python view_session_plots.py abc123def456
```
Output: Opens browser with all plots in subplots

### Example 2: Detailed Analysis
```bash
python view_individual_plots.py abc123def456
```
Output: 
```
session_plots_abc123de/
├── index.html
├── plotly_fig_1234567890.html
├── my_custom_plot_5678901234.html
└── analysis_chart_9876543210.html
```

### Example 3: Check Available Plots
```bash
python view_session_plots.py abc123def456 --list-only
```
Output:
```
Found 5 total plots, 3 after filtering
Filtered plots: ['plotly_fig_1234567890.pickle', 'my_custom_plot_5678901234.pickle', 'analysis_chart_9876543210.pickle']
```

## Troubleshooting

### Common Issues

1. **"Session not found" error**
   - Check that the session ID is correct
   - Ensure the sandbox service is running
   - Verify the session hasn't expired

2. **"No plots found" message**
   - The session might not have generated any plots yet
   - Plots might all start with "plotly_plotly" (which are filtered out)
   - Use `--list-only` to see what plots exist

3. **Import errors**
   - Install missing dependencies: `pip install plotly aiohttp`
   - Ensure you're in the correct directory with `sandbox_client.py` and `plot_template.py`

4. **Browser doesn't open**
   - The HTML files are still created
   - Manually open the generated HTML files
   - Check the console output for file paths

### Debug Mode

For more detailed output, you can modify the scripts to add more debugging:
- The scripts already include detailed console output
- Check the generated HTML files if browser display fails
- Verify plot file sizes and content

## File Structure

After running `view_individual_plots.py`, you'll get:

```
session_plots_{session_id}/
├── index.html              # Main index with links to all plots
├── plot1.html             # Individual plot files
├── plot2.html
└── ...
```

The index.html provides:
- Overview of all plots
- Direct links to individual plot files
- Session information and metadata
- Navigation between plots

## Tips

1. **For quick checks**: Use `view_session_plots.py`
2. **For detailed analysis**: Use `view_individual_plots.py`
3. **For sharing**: Use individual view and share the entire folder
4. **For archiving**: Individual view creates permanent files
5. **For comparison**: Combined view shows all plots together

## Session ID Format

Session IDs are typically UUIDs like:
- `07d7ea02-ca2b-4874-9760-ce881ba454ab`
- `abc123def456` (shortened versions also work)

You can find session IDs in:
- Chainlit application logs
- Sandbox service logs
- Database records
- Application state
