#!/usr/bin/env python3
"""
Test script to verify the fix for the indicator_options KeyError
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from parser_graph import build_partner_prompt

def test_partner_prompts():
    """Test that all partners can build prompts without errors"""
    
    # List of partners from the YAML file
    partners = [
        "oksigen",
        "engie_ec_ent_em", 
        "engie_ec_gc_prive",
        "engie_ec_gc_publics",
        "siplec",
        "veolia",
        "cyrisea_cd92",
        "alterea",
        "cyrisea_cd74",
        "groupeherve_alerteo",
        "groupeherve_hervethemique",
        "groupeherve_agences",
        "demathieu_bard",
        "demathieu_bard_chantiers",
        "demathieu_bard_patrimoine",
        "vincienergies"
    ]
    
    print("Testing partner prompt generation...")
    
    for partner in partners:
        try:
            prompt = build_partner_prompt(partner)
            print(f"✅ {partner}: Successfully generated prompt ({len(prompt)} chars)")
        except Exception as e:
            print(f"❌ {partner}: Error - {str(e)}")
            return False
    
    print("\n🎉 All partner prompts generated successfully!")
    return True

if __name__ == "__main__":
    success = test_partner_prompts()
    sys.exit(0 if success else 1)
