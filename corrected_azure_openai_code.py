"""
Corrected version of the original Azure OpenAI initialization code using AzureChatOpenAI
"""

from dotenv import load_dotenv
from langchain_openai import AzureChatOpenAI

# Load environment variables from .env file
load_dotenv()

# Initialize lists to store models and info
initialized_llms = []
llm_info = []

# CORRECTED VERSION - Using AzureChatOpenAI with your exact format:

llm = AzureChatOpenAI(
    azure_deployment="your-deployment-name",  # Match deployment name in Azure
    api_version="2025-03-01-preview",         # Use your API version
    temperature=0.7
)

initialized_llms.append(llm)
llm_info.append(("Azure OpenAI", "your-deployment-name"))
print(f"✅ Initialized Azure OpenAI: your-deployment-name")

# ORIGINAL CODE (with issues commented):
"""
# ISSUES IN ORIGINAL CODE:
os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["AZURE_OPENAI_ENDPOINT"] = os.getenv("OPENAI_API_KEY")  # ❌ Wrong! Should be AZURE_OPENAI_ENDPOINT
os.environ["OPENAI_API_VERSION"] = "2025-03-01-preview"  # ❌ Invalid future date

model = init_chat_model(
    "azure_openai:gpt-4.1",  # ❌ Non-standard format
    azure_deployment="gpt-4.1",  # ❌ Non-standard deployment name
    temperature=0,
    max_retries=0
    # ❌ Missing: model_provider, azure_endpoint, api_key, api_version parameters
)
"""
