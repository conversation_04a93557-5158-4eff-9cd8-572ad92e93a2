# Energy Manager Copilot

A comprehensive energy management system built with FastAPI, LangChain, and Chainlit.

## Prerequisites

- Python 3.13 or higher
- Docker and Docker Compose
- UV package manager

## Installation

1. **Clone the repository**


2. **Set up Python environment with UV**
```bash
# Install UV if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Create and activate virtual environment
uv venv
source .venv/bin/activate  # On Linux/Mac
# or
.venv\Scripts\activate  # On Windows

# Install dependencies
uv pip install -e .
```

3. **Set up environment variables**
Create a `.env` file in the root directory with the following variables:
```env
# Database Configuration
POSTGRES_USER=user_test
POSTGRES_PASSWORD=password_test
POSTGRES_DB=chainlit_db
POSTGRES_PORT=5432

# API Keys (Add your API keys here)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_api_key
```

## Database Setup

1. **Start the PostgreSQL database**
```bash
docker compose up -d postgres_db
```

2. **Verify database connection**
```bash
# Check if the container is running
docker ps

# Check database logs
docker logs chainlit_postgres_db
```

The database will be automatically initialized with the schema defined in `init.sql`.

## Sandbox Setup

1. **Set up the Sandbox API**
```bash
# Navigate to the sandbox API directory
cd python-sandbox-api

# Build and start the sandbox service
docker compose up --build
```

The sandbox service will be available at `http://localhost:8004` and provides:
- Isolated execution environment
- Resource limits (CPU, memory)
- Network access control
- File system restrictions
- Session management
- Automatic cleanup of inactive sessions

### Sandbox Client Usage

The project includes a `SandboxClient` class for interacting with the sandbox service. Here's a basic example:

```python
from sandbox_client import SandboxClient

# Initialize the client
sandbox = SandboxClient(base_url="http://localhost:8004")

# Create a new session
session = await sandbox.create_session()

# Execute code in the sandbox
result = await sandbox.execute_code(
    session_id=session["session_id"],
    code="print('Hello from sandbox!')",
    timeout_seconds=30
)

# Upload files to the sandbox
await sandbox.upload_file(
    session_id=session["session_id"],
    file_path="path/to/file.csv",
    variable_name="data"
)

# Download plots from the sandbox
plot_data = await sandbox.download_plot(
    session_id=session["session_id"],
    plot_name="my_plot.png"
)
```

### Sandbox Security Considerations

1. **Resource Limits**
   - Set appropriate memory limits based on your needs
   - Configure CPU limits to prevent resource exhaustion
   - Monitor sandbox usage and adjust limits accordingly

2. **Network Access**
   - Disable network access if not required
   - Use allowlist for specific domains if needed
   - Monitor network usage for security

3. **Session Management**
   - Set appropriate session timeouts
   - Implement session cleanup for inactive sessions
   - Monitor session usage and adjust limits

4. **File System**
   - Restrict file system access to necessary directories
   - Implement file size limits
   - Monitor file operations for security

## Running the Application

1. **Start the Chainlit application**
```bash
chainlit run chainlit_app_simple.py
```

The application will be available at `http://localhost:8000`

