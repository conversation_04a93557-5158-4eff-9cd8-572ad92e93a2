-- ==============================================
-- CHAINLIT DATA LAYER TABLES (Lowercase Table Names, Quoted CamelCase Column Names, TEXT for IDs & Timestamps)
-- ==============================================

-- Drop the ENUM type if it exists, then recreate it, to handle potential re-runs of the script
DROP TYPE IF EXISTS "StepType" CASCADE; -- CASCADE will drop dependent columns, which we'll recreate
CREATE TYPE "StepType" AS ENUM ('assistant_message', 'embedding', 'llm', 'retrieval', 'rerank', 'run', 'system_message', 'tool', 'undefined', 'user_message');

CREATE TABLE IF NOT EXISTS users (
    "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(), -- TEXT for ID
    "identifier" TEXT NOT NULL UNIQUE,
    "metadata" JSONB,
    "createdAt" TEXT -- Store as ISO string (as <PERSON><PERSON> seems to send strings)
    -- "updatedAt" TEXT, -- Add if Chainlit expects/sends it
);
CREATE INDEX IF NOT EXISTS users_identifier_idx ON users("identifier");

CREATE TABLE IF NOT EXISTS threads (
    "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(), -- TEXT for ID
    "createdAt" TEXT,
    "name" TEXT,
    "userId" TEXT,         -- TEXT to match users."id"
    "userIdentifier" TEXT,
    "tags" TEXT[],
    "metadata" JSONB,
    "deletedAt" TEXT,      -- Added from one of your earlier schemas, type TEXT
    FOREIGN KEY ("userId") REFERENCES users("id") ON DELETE SET NULL
);
CREATE INDEX IF NOT EXISTS threads_userId_idx ON threads("userId");
CREATE INDEX IF NOT EXISTS threads_userIdentifier_idx ON threads("userIdentifier");

CREATE TABLE IF NOT EXISTS steps (
    "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "type" "StepType" NOT NULL,
    "threadId" TEXT NOT NULL,
    "parentId" TEXT,
    "streaming" BOOLEAN NOT NULL DEFAULT FALSE,
    "waitForAnswer" BOOLEAN,
    "isError" BOOLEAN DEFAULT FALSE,
    "metadata" JSONB,
    "tags" TEXT[],
    "input" TEXT,
    "output" TEXT,
    "createdAt" TEXT,
    "command" TEXT,
    "start" TEXT,
    "end" TEXT,
    "generation" JSONB,
    "showInput" TEXT,
    "language" TEXT,
    "indent" INT,
    "defaultOpen" BOOLEAN DEFAULT FALSE, -- ADDED THIS COLUMN
    FOREIGN KEY ("threadId") REFERENCES threads("id") ON DELETE CASCADE,
    FOREIGN KEY ("parentId") REFERENCES steps("id") ON DELETE CASCADE
);
-- Ensure other indexes are still there
CREATE INDEX IF NOT EXISTS steps_threadId_idx ON steps("threadId");
CREATE INDEX IF NOT EXISTS steps_parentId_idx ON steps("parentId");

CREATE TABLE IF NOT EXISTS elements (
    "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(), -- TEXT for ID
    "threadId" TEXT,     -- TEXT to match threads."id"
    "type" TEXT,
    "url" TEXT,
    "chainlitKey" TEXT,
    "name" TEXT NOT NULL,
    "display" TEXT,
    "objectKey" TEXT,
    "size" TEXT,
    "page" INT,
    "language" TEXT,
    "forId" TEXT,        -- TEXT to match steps."id"
    "mime" TEXT,
    "props" JSONB,
    -- "createdAt" TEXT, -- Add if needed and Chainlit sends as string
    -- "updatedAt" TEXT, -- Add if needed
    FOREIGN KEY ("threadId") REFERENCES threads("id") ON DELETE CASCADE,
    FOREIGN KEY ("forId") REFERENCES steps("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS elements_threadId_idx ON elements("threadId");
CREATE INDEX IF NOT EXISTS elements_forId_idx ON elements("forId");

CREATE TABLE IF NOT EXISTS feedbacks (
    "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(), -- TEXT for ID
    "forId" TEXT NOT NULL,     -- TEXT to match steps."id"
    "threadId" TEXT NOT NULL,  -- TEXT to match threads."id"
    "value" INT NOT NULL,
    "comment" TEXT,
    -- "createdAt" TEXT, -- Add if needed
    -- "updatedAt" TEXT, -- Add if needed
    FOREIGN KEY ("threadId") REFERENCES threads("id") ON DELETE CASCADE,
    FOREIGN KEY ("forId") REFERENCES steps("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS feedbacks_threadId_idx ON feedbacks("threadId");
CREATE INDEX IF NOT EXISTS feedbacks_forId_idx ON feedbacks("forId");

-- Ensure your app_users, partners, app_user_partners, and checkpoints tables are also included
-- in the full init.sql file.