version: '3.8'

services:
  postgres_db:
    image: postgres:17
    container_name: chainlit_postgres_db
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-user_test}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password_test}
      POSTGRES_DB: ${POSTGRES_DB:-chainlit_db}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_chainlit_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql # Script d'initialisation
    restart: unless-stopped

volumes:
  postgres_chainlit_data:

